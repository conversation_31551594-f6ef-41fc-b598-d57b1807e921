// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://github.com/alvestrand/webrtc-media-streams/blob/master/explainer.md#api
// TODO(guidou): Add standards link when available.

enum RTCVideoCodecType {
    "vp8",
    "vp9",
    "h264",
    "av1",
};

enum RTCDecodeTargetIndication {
    "not-present",
    "discardable",
    "switch",
    "required",
};

dictionary RTCEncodedVideoFrameMetadata {
    // Encoding related metadata
    long long frameId;
    sequence<long long> dependencies;
    unsigned short width;
    unsigned short height;
    long spatialIndex;
    long temporalIndex;

    // RTP related metadata
    unsigned long synchronizationSource;
    sequence<unsigned long> contributingSources;
    octet payloadType;
    DOMString mimeType;
    [RuntimeEnabled=RTCEncodedVideoFrameAdditionalMetadata]
    long long timestamp;     // Presentation timestamp, microseconds.
    unsigned long rtpTimestamp;
    [RuntimeEnabled=RTCEncodedFrameTimestamps] DOMHighResTimeStamp receiveTime;
    [RuntimeEnabled=RTCEncodedFrameTimestamps] DOMHighResTimeStamp captureTime;
    [RuntimeEnabled=RTCEncodedFrameTimestamps] DOMHighResTimeStamp senderCaptureTimeOffset;
};
