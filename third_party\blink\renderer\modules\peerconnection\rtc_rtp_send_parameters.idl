// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/mst-content-hint/#dom-rtcdegradationpreference
enum RTCDegradationPreference {
    "maintain-framerate",
    "maintain-resolution",
    "balanced"
};

// https://w3c.github.io/webrtc-pc/#rtcsendrtpparameters
dictionary RTCRtpSendParameters : RTCRtpParameters {
    required DOMString                          transactionId;
    required sequence<RTCRtpEncodingParameters> encodings;
             RTCDegradationPreference           degradationPreference;
};
