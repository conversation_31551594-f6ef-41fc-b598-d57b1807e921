// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#rtcrtpsender-interface
[Exposed=Window]
interface RTCRtpSender {
    readonly attribute MediaStreamTrack? track;
    readonly attribute RTCDtlsTransport? transport;
    readonly attribute RTCDtlsTransport? rtcpTransport;
    [CallWith=ScriptState, HighEntropy, Measure] static RTCRtpCapabilities? getCapabilities(DOMString kind);
    // options argument is defined by https://w3c.github.io/webrtc-extensions/#rtcrtpsender-setparameters-keyframe
    [CallWith=ScriptState, RaisesException] Promise<undefined> setParameters(RTCRtpSendParameters parameters, optional RTCSetParameterOptions options = {});
    RTCRtpSendParameters getParameters();
    [Measure, CallWith=ScriptState, RaisesException] Promise<undefined> replaceTrack(MediaStreamTrack? withTrack);
    [Measure] readonly attribute RTCDTMFSender? dtmf;
    [Measure, RaisesException] void setStreams(MediaStream... streams);
    [CallWith=ScriptState] Promise<RTCStatsReport> getStats();
    [Measure,
     CallWith=ScriptState,
     RaisesException] RTCInsertableStreams createEncodedStreams();

    [RuntimeEnabled=RTCRtpTransport]
    Promise<sequence<RTCRtpSendStream>> replaceSendStreams();

    [RuntimeEnabled=RTCRtpScriptTransform, RaisesException=Setter]
    attribute RTCRtpScriptTransform? transform;
};
