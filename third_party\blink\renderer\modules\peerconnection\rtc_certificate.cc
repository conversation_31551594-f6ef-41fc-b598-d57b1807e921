/*
 * Copyright (C) 2015 Google Inc. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer
 *    in the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name of Google Inc. nor the names of its contributors
 *    may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, E<PERSON>EMPLARY, OR <PERSON>NSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * <PERSON>IMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "third_party/blink/renderer/modules/peerconnection/rtc_certificate.h"

#include "base/memory/ptr_util.h"
#include "third_party/blink/renderer/platform/bindings/v8_binding.h"
#include "third_party/webrtc/rtc_base/ssl_certificate.h"

namespace blink {

RTCCertificate::RTCCertificate(
    webrtc::scoped_refptr<webrtc::RTCCertificate> certificate)
    : certificate_(std::move(certificate)) {}

DOMTimeStamp RTCCertificate::expires() const {
  return static_cast<DOMTimeStamp>(certificate_->Expires());
}

HeapVector<Member<RTCDtlsFingerprint>> RTCCertificate::getFingerprints() {
  std::unique_ptr<webrtc::SSLCertificateStats> first_certificate_stats =
      certificate_->GetSSLCertificate().GetStats();

  HeapVector<Member<RTCDtlsFingerprint>> fingerprints;
  for (webrtc::SSLCertificateStats* certificate_stats =
           first_certificate_stats.get();
       certificate_stats; certificate_stats = certificate_stats->issuer.get()) {
    RTCDtlsFingerprint* fingerprint = RTCDtlsFingerprint::Create();
    fingerprint->setAlgorithm(
        WTF::String::FromUTF8(certificate_stats->fingerprint_algorithm));
    fingerprint->setValue(
        WTF::String::FromUTF8(certificate_stats->fingerprint).LowerASCII());
    fingerprints.push_back(fingerprint);
  }

  return fingerprints;
}

}  // namespace blink
