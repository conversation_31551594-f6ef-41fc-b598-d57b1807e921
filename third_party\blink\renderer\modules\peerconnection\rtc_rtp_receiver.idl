// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#rtcrtpreceiver-interface
[Exposed=Window]
interface RTCRtpReceiver {
    readonly attribute MediaStreamTrack track;
    readonly attribute RTCDtlsTransport? transport;
    readonly attribute RTCDtlsTransport? rtcpTransport;
    // https://henbos.github.io/webrtc-extensions/#dom-rtcrtpreceiver-playoutdelayhint
    [RaisesException=Setter, Measure] attribute double? playoutDelayHint;
    // https://w3c.github.io/webrtc-extensions/#dom-rtcrtpreceiver-jitterbuffertarget
    [RaisesException=Setter, Measure, RuntimeEnabled=RTCJitterBufferTarget] attribute double? jitterBufferTarget;
    [CallWith=ScriptState, HighEntropy, Measure] static RTCRtpCapabilities? getCapabilities(DOMString kind);
    RTCRtpReceiveParameters               getParameters();
    [CallWith=ScriptState, RaisesException] sequence<RTCRtpSynchronizationSource> getSynchronizationSources();
    [CallWith=ScriptState, RaisesException] sequence<RTCRtpContributingSource> getContributingSources();
    [CallWith=ScriptState] Promise<RTCStatsReport> getStats();
    [Measure,
     CallWith=ScriptState,
     RaisesException] RTCInsertableStreams createEncodedStreams();
    [RuntimeEnabled=RTCRtpScriptTransform, RaisesException=Setter]
    attribute RTCRtpScriptTransform? transform;
};
