// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-priority/#rtc-priority-type
enum RTCPriorityType {
    "very-low",
    "low",
    "medium",
    "high"
};

// https://github.com/w3c/webrtc-extensions/pull/221
dictionary RTCResolutionRestriction {
    unsigned long   maxWidth;
    unsigned long   maxHeight;
};

// https://w3c.github.io/webrtc-pc/#rtcrtpencodingparameters*
dictionary RTCRtpEncodingParameters : RTCRtpCodingParameters {
    boolean         active = true;
    unsigned long   maxBitrate;
    double          scaleResolutionDownBy;
    // https://github.com/w3c/webrtc-extensions/pull/221
    [RuntimeEnabled=RTCRtpScaleResolutionDownTo]
    RTCResolutionRestriction scaleResolutionDownTo;
    // https://w3c.github.io/webrtc-priority/#encoding-parameters
    RTCPriorityType priority = "low";
    RTCPriorityType networkPriority = "low";
    // https://w3c.github.io/webrtc-extensions/#dom-rtcrtpencodingparameters-maxframerate
    double          maxFramerate;
    // https://w3c.github.io/webrtc-svc/#dom-rtcrtpencodingparameters-scalabilitymode
    [RuntimeEnabled=RTCSvcScalabilityMode] DOMString scalabilityMode;
    // https://w3c.github.io/webrtc-extensions/#dom-rtcrtpencodingparameters-adaptiveptime
    boolean adaptivePtime = false;
    // https://w3c.github.io/webrtc-extensions/#dom-rtcrtpencodingparameters-codec
    [RuntimeEnabled=RTCRtpEncodingParametersCodec]
    RTCRtpCodec codec;
};
