// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://github.com/alvestrand/webrtc-media-streams/blob/master/explainer.md#api
// TODO(guidou): Add standards link when available.

dictionary RTCEncodedAudioFrameMetadata {
    unsigned long synchronizationSource;
    sequence<unsigned long> contributingSources;
    octet payloadType;
    DOMString mimeType;
    unsigned short? sequenceNumber;
    unsigned long rtpTimestamp;
    [RuntimeEnabled=RTCEncodedFrameTimestamps] DOMHighResTimeStamp receiveTime;
    [RuntimeEnabled=RTCEncodedFrameTimestamps] DOMHighResTimeStamp captureTime;
    [RuntimeEnabled=RTCEncodedFrameTimestamps] DOMHighResTimeStamp senderCaptureTimeOffset;
};
