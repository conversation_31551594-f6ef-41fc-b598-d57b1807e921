// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

[
    ImplementedAs=InternalsRTCPeerConnection
] partial interface Internals {
    long peerConnectionCount();
    readonly attribute long peerConnectionCountLimit;

    [CallWith=ScriptState] Promise<any> waitForPeerConnectionDispatchEventsTaskCreated(RTCPeerConnection connection);
};
