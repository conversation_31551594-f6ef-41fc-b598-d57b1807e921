// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

//https://w3c.github.io/webrtc-pc/#rtcpeerconnectioniceerrorevent

[
    Exposed=Window
] interface RTCPeerConnectionIceErrorEvent : Event {
  constructor (DOMString type, RTCPeerConnectionIceErrorEventInit eventInitDict);
  readonly attribute DOMString? address;
  readonly attribute unsigned short? port;
  [DeprecateAs=HostCandidateAttributeGetter] readonly attribute DOMString hostCandidate;
  readonly attribute DOMString url;
  readonly attribute unsigned short errorCode;
  readonly attribute USVString errorText;
};