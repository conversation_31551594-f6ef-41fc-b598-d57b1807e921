// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://www.w3.org/TR/webrtc-encoded-transform/#scriptTransform

enum RTCEncodedVideoFrameType {
    "empty",
    "key",
    "delta",
};

// TODO(crbug.com/1052765): Align with WebCodecs definition once it is stable.
[
    Exposed=(Window, DedicatedWorker)
] interface RTCEncodedVideoFrame {
    [Measure, RaisesException, CallWith=ExecutionContext]
    constructor (RTCEncodedVideoFrame originalFrame, optional RTCEncodedVideoFrameOptions options = {});
    readonly attribute RTCEncodedVideoFrameType type;
    [Measure] readonly attribute unsigned long timestamp;  // RTP timestamp.
    [CallWith=ExecutionContext] attribute ArrayBuffer data;
    [CallWith=ExecutionContext] RTCEncodedVideoFrameMetadata getMetadata();
    [RuntimeEnabled=RTCEncodedFrameSetMetadata, Measure, RaisesException, CallWith=ExecutionContext]
    void setMetadata(RTCEncodedVideoFrameMetadata metadata);
    [CallWith=ExecutionContext] stringifier;
};
