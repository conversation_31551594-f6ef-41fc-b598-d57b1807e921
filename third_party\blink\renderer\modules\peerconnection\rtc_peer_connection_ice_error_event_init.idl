// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#rtcpeerconnectioniceerrorevent

dictionary RTCPeerConnectionIceErrorEventInit : EventInit {
  DOMString? address;
  unsigned short? port;
  DOMString hostCandidate;
  DOMString url;
  required unsigned short errorCode;
  USVString errorText;
};
