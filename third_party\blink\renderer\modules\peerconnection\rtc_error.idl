// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#dfn-rtcerrordetailtype
enum RTCErrorDetailType {
    "data-channel-failure",
    "dtls-failure",
    "fingerprint-failure",
    "idp-bad-script-failure",
    "idp-execution-failure",
    "idp-load-failure",
    "idp-need-login",
    "idp-timeout",
    "idp-tls-failure",
    "idp-token-expired",
    "idp-token-invalid",
    "sctp-failure",
    "sdp-syntax-error",
    "hardware-encoder-not-available",
    "hardware-encoder-error",

    // Deviation from specification see crbug.com/*********
    "no-info",
};

// https://w3c.github.io/webrtc-pc/#dfn-rtcerror
[
    Exposed=Window
] interface RTCError : DOMException {
    constructor(RTCErrorInit init, optional DOMString message = "");
    readonly attribute RTCErrorDetailType errorDetail;
    readonly attribute long? sdpLineNumber;
    readonly attribute long? httpRequestStatusCode;
    readonly attribute long? sctpCauseCode;
    readonly attribute unsigned long? receivedAlert;
    readonly attribute unsigned long? sentAlert;
};
