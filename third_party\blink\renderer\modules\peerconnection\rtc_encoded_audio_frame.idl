// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://www.w3.org/TR/webrtc-encoded-transform/#scriptTransform

// TODO(crbug.com/1052765): Align with WebCodecs definition once it is stable.
[
    Exposed=(Window, DedicatedWorker)
] interface RTCEncodedAudioFrame {
    [Measure, CallWith=ExecutionContext, RaisesException]
    constructor (RTCEncodedAudioFrame originalFrame, optional RTCEncodedAudioFrameOptions options = {});
    [Measure] readonly attribute unsigned long timestamp;  // RTP timestamp.
    [CallWith=ExecutionContext] attribute ArrayBuffer data;
    [CallWith=ExecutionContext] RTCEncodedAudioFrameMetadata getMetadata();
    [CallWith=ExecutionContext] stringifier;
    [RuntimeEnabled=RTCEncodedFrameSetMetadata, CallWith=ExecutionContext, Measure, RaisesException]
    void setMetadata(RTCEncodedAudioFrameMetadata metadata);
};
