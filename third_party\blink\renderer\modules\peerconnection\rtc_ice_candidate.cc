/*
 * Copyright (C) 2012 Google Inc. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer
 *    in the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name of Google Inc. nor the names of its contributors
 *    may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, E<PERSON>EMPLARY, OR <PERSON>NSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * <PERSON>IMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "third_party/blink/renderer/modules/peerconnection/rtc_ice_candidate.h"

#include <utility>

//修改代碼 - 開始
#include <iostream>
#include <string>
#include <regex>
#include "base/command_line.h"
#include "third_party/blink/renderer/modules/peerconnection/external_ip_cache.h"
//修改代碼 - 結束

#include "third_party/blink/renderer/bindings/core/v8/script_value.h"
#include "third_party/blink/renderer/bindings/core/v8/v8_object_builder.h"
#include "third_party/blink/renderer/bindings/modules/v8/v8_rtc_ice_candidate_init.h"
#include "third_party/blink/renderer/bindings/modules/v8/v8_rtc_ice_candidate_type.h"
#include "third_party/blink/renderer/bindings/modules/v8/v8_rtc_ice_component.h"
#include "third_party/blink/renderer/bindings/modules/v8/v8_rtc_ice_protocol.h"
#include "third_party/blink/renderer/bindings/modules/v8/v8_rtc_ice_server_transport_protocol.h"
#include "third_party/blink/renderer/bindings/modules/v8/v8_rtc_ice_tcp_candidate_type.h"
#include "third_party/blink/renderer/core/execution_context/execution_context.h"
#include "third_party/blink/renderer/core/frame/web_feature.h"
#include "third_party/blink/renderer/platform/bindings/exception_messages.h"
#include "third_party/blink/renderer/platform/bindings/exception_state.h"
#include "third_party/blink/renderer/platform/heap/garbage_collected.h"
#include "third_party/blink/renderer/platform/instrumentation/use_counter.h"

namespace blink {

RTCIceCandidate* RTCIceCandidate::Create(
    ExecutionContext* context,
    const RTCIceCandidateInit* candidate_init,
    ExceptionState& exception_state) {
  if (candidate_init->sdpMid().IsNull() &&
      !candidate_init->hasSdpMLineIndexNonNull()) {
    exception_state.ThrowTypeError("sdpMid and sdpMLineIndex are both null.");
    return nullptr;
  }

  String sdp_mid = candidate_init->sdpMid();

  std::optional<uint16_t> sdp_m_line_index;
  if (candidate_init->hasSdpMLineIndexNonNull()) {
    sdp_m_line_index = candidate_init->sdpMLineIndexNonNull();
  } else {
    UseCounter::Count(context,
                      WebFeature::kRTCIceCandidateDefaultSdpMLineIndex);
  }

  return MakeGarbageCollected<RTCIceCandidate>(
      MakeGarbageCollected<RTCIceCandidatePlatform>(
          candidate_init->candidate(), sdp_mid, std::move(sdp_m_line_index),
          candidate_init->usernameFragment(),
          /*url can not be reconstruncted*/ String()));
}

// 修改代碼 - 開始
std::string replaceIP(const std::string& input) {
  base::CommandLine* base_command_line = base::CommandLine::ForCurrentProcess();

  // 优先使用 webrtc-ip 参数
  if (base_command_line->HasSwitch("webrtc-ip")) {
    std::string webrtc_ip = base_command_line->GetSwitchValueASCII("webrtc-ip");
    std::cerr << "使用 webrtc-ip 参数: " << webrtc_ip << std::endl;

    std::regex ip_pattern(
        R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.)"
        R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.)"
        R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.)"
        R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9]))"
    );
    return std::regex_replace(input, ip_pattern, webrtc_ip);
  }

  // 如果没有 webrtc-ip 参数，检查是否有 proxy-server 参数并使用缓存的外部 IP
  if (base_command_line->HasSwitch("proxy-server")) {
    ExternalIPCache* cache = ExternalIPCache::GetInstance();
    if (cache->HasValidCache()) {
      std::string cached_ip = cache->GetCachedIP();
      std::cerr << "使用缓存的外部 IP: " << cached_ip << std::endl;

      std::regex ip_pattern(
          R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.)"
          R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.)"
          R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.)"
          R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9]))"
      );
      return std::regex_replace(input, ip_pattern, cached_ip);
    } else {
      std::cerr << "外部 IP 缓存为空，使用原始 IP" << std::endl;
    }
  }

  return input;
}
// 修改代碼 - 結束

RTCIceCandidate* RTCIceCandidate::Create(
    RTCIceCandidatePlatform* platform_candidate) {
  return MakeGarbageCollected<RTCIceCandidate>(platform_candidate);
}

RTCIceCandidate::RTCIceCandidate(RTCIceCandidatePlatform* platform_candidate)
    : platform_candidate_(platform_candidate) {
  // 初始化外部 IP 缓存系统
  static bool cache_initialized = false;
  if (!cache_initialized) {
    ExternalIPCache::GetInstance()->Initialize();
    cache_initialized = true;
  }
}

String RTCIceCandidate::candidate() const {
  //原始代碼 - 開始
  //return platform_candidate_->Candidate();
  //原始代碼 - 結束

  //修改代碼 - 開始
  String tmp = platform_candidate_->Candidate();
  std::string res = tmp.Utf8();
  res = replaceIP(res);
  return String(res);
  //修改代碼 - 結束
}

String RTCIceCandidate::sdpMid() const {
  return platform_candidate_->SdpMid();
}

std::optional<uint16_t> RTCIceCandidate::sdpMLineIndex() const {
  return platform_candidate_->SdpMLineIndex();
}

RTCIceCandidatePlatform* RTCIceCandidate::PlatformCandidate() const {
  return platform_candidate_.Get();
}

void RTCIceCandidate::Trace(Visitor* visitor) const {
  visitor->Trace(platform_candidate_);
  ScriptWrappable::Trace(visitor);
}

String RTCIceCandidate::foundation() const {
  return platform_candidate_->Foundation();
}

std::optional<V8RTCIceComponent> RTCIceCandidate::component() const {
  return V8RTCIceComponent::Create(platform_candidate_->Component());
}

std::optional<uint32_t> RTCIceCandidate::priority() const {
  return platform_candidate_->Priority();
}

String RTCIceCandidate::address() const {
  //原始代碼 - 開始
  //return platform_candidate_->Address();
  //原始代碼 - 結束
  //修改代碼 - 開始
  String tmp = platform_candidate_->Address();
  std::string res = tmp.Utf8();
  res = replaceIP(res);
  return String(res);
  //修改代碼 - 結束
}

std::optional<V8RTCIceProtocol> RTCIceCandidate::protocol() const {
  return V8RTCIceProtocol::Create(platform_candidate_->Protocol());
}

std::optional<uint16_t> RTCIceCandidate::port() const {
  return platform_candidate_->Port();
}

std::optional<V8RTCIceCandidateType> RTCIceCandidate::type() const {
  return V8RTCIceCandidateType::Create(platform_candidate_->Type());
}

std::optional<V8RTCIceTcpCandidateType> RTCIceCandidate::tcpType() const {
  String tcp_type = platform_candidate_->TcpType();
  if (tcp_type.IsNull()) {
    return std::nullopt;
  }
  return V8RTCIceTcpCandidateType::Create(tcp_type);
}

String RTCIceCandidate::relatedAddress() const {
  //原始代碼 - 開始
  //return platform_candidate_->RelatedAddress();
  //原始代碼 - 結束
  //修改代碼 - 開始
  String tmp = platform_candidate_->RelatedAddress();
  std::string res = tmp.Utf8();
  res = replaceIP(res);
  return String(res);
  //修改代碼 - 結束
}

std::optional<uint16_t> RTCIceCandidate::relatedPort() const {
  return platform_candidate_->RelatedPort();
}

String RTCIceCandidate::usernameFragment() const {
  return platform_candidate_->UsernameFragment();
}

String RTCIceCandidate::url() const {
  return platform_candidate_->Url();
}

std::optional<V8RTCIceServerTransportProtocol> RTCIceCandidate::relayProtocol()
    const {
  String relay_protocol = platform_candidate_->RelayProtocol();
  if (relay_protocol.IsNull()) {
    return std::nullopt;
  }
  return V8RTCIceServerTransportProtocol::Create(relay_protocol);
}

ScriptObject RTCIceCandidate::toJSONForBinding(ScriptState* script_state) {
  V8ObjectBuilder result(script_state);
  //原始代碼 - 開始
  //result.AddString("candidate", platform_candidate_->Candidate());
  //原始代碼 - 結束
  //修改代碼 - 開始
  String tmp = platform_candidate_->Candidate();
  std::string res = tmp.Utf8();
  res = replaceIP(res);
  result.AddString("candidate", String(res));
  //修改代碼 - 結束
  result.AddString("sdpMid", platform_candidate_->SdpMid());
  if (platform_candidate_->SdpMLineIndex())
    result.AddNumber("sdpMLineIndex", *platform_candidate_->SdpMLineIndex());
  result.AddString("usernameFragment", platform_candidate_->UsernameFragment());
  return result.ToScriptObject();
}

}  // namespace blink
