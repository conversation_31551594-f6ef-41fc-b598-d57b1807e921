// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#dom-rtcicerole
enum RTCIceRole {
    "controlling",
    "controlled",
};

// https://w3c.github.io/webrtc-pc/#dom-rtcicetransportstate
enum RTCIceTransportState {
    "new",
    "checking",
    "connected",
    "completed",
    "disconnected",
    "failed",
    "closed",
};

// https://w3c.github.io/webrtc-pc/#dom-rtcicegatheringstate
enum RTCIceGatheringState {
    "new",
    "gathering",
    "complete"
};

// https://w3c.github.io/webrtc-pc/#rtcicetransport
[
   ActiveScriptWrappable,
   Exposed=Window
] interface RTCIceTransport : EventTarget {
    // TODO(github.com/w3c/webrtc-ice/issues/4): role is non-null in the
    // WebRTC-PC specification.
    [Measure] readonly attribute RTCIceRole? role;
    [Measure] readonly attribute RTCIceTransportState state;
    [Measure] readonly attribute RTCIceGatheringState gatheringState;
    [Measure] sequence<RTCIceCandidate> getLocalCandidates();
    [Measure] sequence<RTCIceCandidate> getRemoteCandidates();
    [Measure] RTCIceCandidatePair? getSelectedCandidatePair();
    [Measure] RTCIceParameters? getLocalParameters();
    [Measure] RTCIceParameters? getRemoteParameters();
    attribute EventHandler onstatechange;
    attribute EventHandler ongatheringstatechange;
    attribute EventHandler onselectedcandidatepairchange;
};
