// Copyright 2016 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#rtcicetransportpolicy-enum

enum RTCIceTransportPolicy {
    "relay",
    "all"
};

// https://w3c.github.io/webrtc-pc/#rtcbundlepolicy-enum

enum RTCBundlePolicy {
    "balanced",
    "max-compat",
    "max-bundle"
};

// https://w3c.github.io/webrtc-pc/#rtcrtcpmuxpolicy-enum

enum RTCRtcpMuxPolicy {
    "negotiate",
    "require"
};

// https://w3c.github.io/webrtc-pc/#rtcconfiguration-dictionary

dictionary RTCConfiguration {
    sequence<RTCIceServer> iceServers;
    // TODO(foolip): |iceTransportPolicy| default should be "all", but it is
    // omitted to allow fallback to |iceTransports| if not specified.
    RTCIceTransportPolicy iceTransportPolicy;
    // TODO(foolip): |iceTransports| is not in the spec.
    // https://crbug.com/659131
    RTCIceTransportPolicy iceTransports;
    RTCBundlePolicy bundlePolicy = "balanced";
    RTCRtcpMuxPolicy rtcpMuxPolicy = "require";
    // TODO(foolip): DOMString peerIdentity;
    sequence<RTCCertificate> certificates;
    [EnforceRange] octet iceCandidatePoolSize = 0;
    [RuntimeEnabled=RtcAudioJitterBufferMaxPackets] long rtcAudioJitterBufferMaxPackets;
    [RuntimeEnabled=RtcAudioJitterBufferMaxPackets] boolean rtcAudioJitterBufferFastAccelerate;
    [RuntimeEnabled=RtcAudioJitterBufferMaxPackets] long rtcAudioJitterBufferMinDelayMs;
    boolean encodedInsertableStreams = false;
};
