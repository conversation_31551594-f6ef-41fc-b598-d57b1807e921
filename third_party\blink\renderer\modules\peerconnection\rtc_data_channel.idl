/*
 * Copyright (C) 2012 Google Inc. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1.  Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 * 2.  Redistributions in binary form must reproduce the above copyright
 *     notice, this list of conditions and the following disclaimer in the
 *     documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF ME<PERSON><PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
 * DAMAGE.
 */

// https://w3c.github.io/webrtc-pc/#idl-def-rtcdatachannelstate

enum RTCDataChannelState {
    "connecting",
    "open",
    "closing",
    "closed"
};

// https://w3c.github.io/webrtc-pc/#rtcdatachannel

[
    ActiveScriptWrappable,
    Exposed(Window StableBlinkFeatures, DedicatedWorker TransferableRTCDataChannel)
] interface RTCDataChannel : EventTarget {
    readonly attribute USVString label;
    readonly attribute boolean ordered;
    readonly attribute unsigned short? maxPacketLifeTime;
    readonly attribute unsigned short? maxRetransmits;
    readonly attribute USVString protocol;
    readonly attribute boolean negotiated;
    readonly attribute unsigned short? id;
    readonly attribute RTCDataChannelState readyState;
    readonly attribute unsigned long bufferedAmount;
             attribute unsigned long bufferedAmountLowThreshold;
             attribute EventHandler onopen;
             attribute EventHandler onbufferedamountlow;
             attribute EventHandler onerror;
             attribute EventHandler onclosing;
             attribute EventHandler onclose;
    void close();
             attribute EventHandler onmessage;
    attribute BinaryType binaryType;
    [RaisesException] void send(USVString data);
    [RaisesException] void send(Blob data);
    [RaisesException] void send(ArrayBuffer data);
    [RaisesException] void send(ArrayBufferView data);

    // https://w3c.github.io/webrtc-priority/#datachannel
    [RuntimeEnabled=RTCDataChannelPriority] readonly attribute RTCPriorityType priority;

    // Non-standard APIs
    [Measure] readonly attribute boolean reliable;
};
