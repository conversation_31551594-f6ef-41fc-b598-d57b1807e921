// Copyright 2016 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#rtciceserver-dictionary

dictionary RTCIceServer {
    // TODO(foolip): |urls| should be required and |url| is not in the spec.
    // https://crbug.com/659133
    (DOMString or sequence<DOMString>) urls;
    DOMString url;
    DOMString username;
    DOMString credential;
    // TODO(foolip): RTCIceCredentialType credentialType = "password";
};
