# WebRTC IP 替换功能

## 概述

这个功能允许在 WebRTC ICE 候选中替换 IP 地址，支持两种模式：

1. **静态 IP 替换**：使用 `--webrtc-ip` 参数指定固定的 IP 地址
2. **动态外部 IP 替换**：使用 `--proxy-server` 参数，通过 API 异步获取外部 IP 并缓存

## 使用方法

### 方法 1：静态 IP 替换

```bash
chrome --webrtc-ip=*************
```

这将把所有 WebRTC ICE 候选中的 IP 地址替换为 `*************`。

### 方法 2：动态外部 IP 替换

```bash
chrome --proxy-server=http://proxy.example.com:8080
```

这将：
1. 检测到 `proxy-server` 参数
2. 异步请求外部 IP 检测 API（如 https://api.ipify.org）
3. 缓存获取到的外部 IP
4. 每分钟更新一次缓存
5. 在 WebRTC ICE 候选中使用缓存的外部 IP

## 优先级

如果同时指定了两个参数，`--webrtc-ip` 具有更高的优先级。

## 实现细节

### 文件结构

- `external_ip_cache.h/cc`：外部 IP 缓存管理器
- `rtc_ice_candidate.cc`：修改的 ICE 候选处理逻辑

### 核心功能

1. **ExternalIPCache 类**：
   - 单例模式
   - 异步网络请求
   - 线程安全的缓存
   - 定期更新机制

2. **replaceIP 函数**：
   - 检查命令行参数
   - 执行 IP 地址替换
   - 支持正则表达式匹配

### 影响的方法

以下 RTCIceCandidate 方法会应用 IP 替换：
- `candidate()`
- `address()`
- `relatedAddress()`
- `toJSONForBinding()`

## 网络请求

当使用 `--proxy-server` 参数时，系统会：
1. 向 `https://api.ipify.org?format=text` 发送 GET 请求
2. 解析返回的 IP 地址
3. 验证 IP 格式
4. 缓存有效的 IP 地址

## 安全考虑

- 网络请求使用适当的流量注解
- 不发送 cookies 或认证信息
- 设置合理的超时时间
- 验证返回的 IP 地址格式

## 调试

系统会在控制台输出调试信息：
- 参数检测状态
- IP 获取过程
- 缓存更新状态
- IP 替换操作

## 注意事项

1. 外部 IP 检测依赖网络连接
2. 如果网络请求失败，会保持原始 IP
3. 缓存会在浏览器会话期间保持
4. 定期更新确保 IP 地址的时效性

## 测试

可以通过以下方式测试功能：

1. 启动 Chrome 时添加相应参数
2. 打开 WebRTC 应用
3. 检查 ICE 候选中的 IP 地址
4. 观察控制台输出的调试信息
