/*
 * Copyright (C) 2012 Google Inc. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer
 *    in the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name of Google Inc. nor the names of its contributors
 *    may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, E<PERSON>EMPLARY, OR <PERSON>NSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * <PERSON>IMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

// https://w3c.github.io/webrtc-pc/#state-definitions

enum RTCSignalingState {
    "stable",
    "have-local-offer",
    "have-remote-offer",
    "have-local-pranswer",
    "have-remote-pranswer",
    "closed"
};

enum RTCIceConnectionState {
    "new",
    "checking",
    "connected",
    "completed",
    "failed",
    "disconnected",
    "closed"
};

enum RTCPeerConnectionState {
    "new",
    "connecting",
    "connected",
    "disconnected",
    "failed",
    "closed"
};

// https://w3c.github.io/webrtc-pc/#interface-definition

[
    ActiveScriptWrappable,
    Exposed=Window,
    LegacyWindowAlias=webkitRTCPeerConnection,
    LegacyWindowAlias_Measure
] interface RTCPeerConnection : EventTarget {
    [CallWith=ExecutionContext, RaisesException] constructor(optional RTCConfiguration configuration = {});
    [CallWith=ScriptState, RaisesException] Promise<RTCSessionDescriptionInit> createOffer(optional RTCOfferOptions options = {});
    [CallWith=ScriptState, RaisesException] Promise<RTCSessionDescriptionInit> createAnswer(optional RTCAnswerOptions options = {});
    [CallWith=ScriptState, RaisesException] Promise<undefined> setLocalDescription(optional RTCSessionDescriptionInit description = {});
    readonly attribute RTCSessionDescription? localDescription;
    readonly attribute RTCSessionDescription? currentLocalDescription;
    readonly attribute RTCSessionDescription? pendingLocalDescription;
    [CallWith=ScriptState, RaisesException] Promise<undefined> setRemoteDescription(RTCSessionDescriptionInit description);
    readonly attribute RTCSessionDescription? remoteDescription;
    readonly attribute RTCSessionDescription? currentRemoteDescription;
    readonly attribute RTCSessionDescription? pendingRemoteDescription;
    [CallWith=ScriptState, RaisesException, MeasureAs=RTCPeerConnectionAddIceCandidatePromise] Promise<undefined> addIceCandidate(optional RTCIceCandidateInit candidate = {});
    readonly attribute RTCSignalingState signalingState;
    readonly attribute RTCIceGatheringState iceGatheringState;
    readonly attribute RTCIceConnectionState iceConnectionState;
    readonly attribute RTCPeerConnectionState connectionState;
    readonly attribute boolean? canTrickleIceCandidates;
    void restartIce();
    [CallWith=ScriptState] RTCConfiguration getConfiguration();
    [CallWith=ScriptState, RaisesException] void setConfiguration(optional RTCConfiguration configuration = {});
    void close();
    attribute EventHandler onnegotiationneeded;
    attribute EventHandler onicecandidate;
    attribute EventHandler onsignalingstatechange;
    attribute EventHandler oniceconnectionstatechange;
    attribute EventHandler onconnectionstatechange;
    attribute EventHandler onicegatheringstatechange;
    attribute EventHandler onicecandidateerror;

    // https://w3c.github.io/webrtc-pc/#legacy-interface-extensions
    // These methods return Promise<undefined> because having Promise-based versions requires that all overloads return Promises.
    [CallWith=ScriptState, RaisesException] Promise<undefined> createOffer(RTCSessionDescriptionCallback successCallback, RTCPeerConnectionErrorCallback failureCallback, optional RTCOfferOptions options = {});
    [CallWith=ScriptState, RaisesException] Promise<undefined> createAnswer(RTCSessionDescriptionCallback successCallback, RTCPeerConnectionErrorCallback failureCallback);
    // TODO(guidou): The failureCallback argument should be non-optional.
    // TODO(crbug.com/841185): |failureCallback| is not nullable in the spec.
    [CallWith=ScriptState] Promise<undefined> setLocalDescription(RTCSessionDescriptionInit description, VoidFunction successCallback, optional RTCPeerConnectionErrorCallback? failureCallback);
    // TODO(guidou): The failureCallback argument should be non-optional.
    // TODO(crbug.com/841185): |failureCallback| is not nullable in the spec.
    [CallWith=ScriptState] Promise<undefined> setRemoteDescription(RTCSessionDescriptionInit description, VoidFunction successCallback, optional RTCPeerConnectionErrorCallback? failureCallback);
    [CallWith=ScriptState, RaisesException, MeasureAs=RTCPeerConnectionAddIceCandidateLegacy] Promise<undefined> addIceCandidate(RTCIceCandidateInit candidate, VoidFunction successCallback, RTCPeerConnectionErrorCallback failureCallback);

    // https://w3c.github.io/webrtc-pc/#sec.stats-model
    [CallWith=ScriptState, RaisesException] Promise<RTCStatsReport> getStats(optional MediaStreamTrack? selector = null);

    // RTP Media API
    // https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-gettransceivers
    [Measure] sequence<RTCRtpTransceiver> getTransceivers();
    // https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-getsenders
    [Measure] sequence<RTCRtpSender> getSenders();
    // https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-getreceivers
    [Measure] sequence<RTCRtpReceiver> getReceivers();
    // https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-addtransceiver
    [Measure, RaisesException] RTCRtpTransceiver addTransceiver(
        (MediaStreamTrack or DOMString) trackOrKind,
        optional RTCRtpTransceiverInit init = {});
    // https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-addtrack
    [Measure, RaisesException] RTCRtpSender addTrack(MediaStreamTrack track, MediaStream... streams);
    // https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-removetrack
    [Measure, RaisesException] void removeTrack(RTCRtpSender sender);
    // https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-ontrack
    attribute EventHandler ontrack;

    // https://w3c.github.io/webrtc-pc/#peer-to-peer-data-api
    readonly attribute RTCSctpTransport? sctp;
    [CallWith=ScriptState, RaisesException] RTCDataChannel createDataChannel(USVString label, optional RTCDataChannelInit dataChannelDict = {});
    attribute EventHandler ondatachannel;

    // Certificate management
    // https://w3c.github.io/webrtc-pc/#sec.cert-mgmt
    [RaisesException, CallWith=ScriptState] static Promise<RTCCertificate> generateCertificate(AlgorithmIdentifier keygenAlgorithm);

    [SameObject, RuntimeEnabled=RTCRtpTransport] readonly attribute RTCRtpTransport rtpTransport;

    // Non-standard or removed from the spec:
    [Measure] sequence<MediaStream> getLocalStreams();
    [Measure] sequence<MediaStream> getRemoteStreams();
    [Measure, CallWith=ScriptState, RaisesException] void addStream(MediaStream stream);
    [Measure, RaisesException] void removeStream(MediaStream stream);
    [Measure, RaisesException] RTCDTMFSender createDTMFSender(MediaStreamTrack track);
    attribute EventHandler onaddstream;
    attribute EventHandler onremovestream;
};

// https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnectionerrorcallback
callback RTCPeerConnectionErrorCallback = void (DOMException error);

// https://w3c.github.io/webrtc-pc/#dom-rtcsessiondescriptioncallback
callback RTCSessionDescriptionCallback = void (RTCSessionDescriptionInit description);
