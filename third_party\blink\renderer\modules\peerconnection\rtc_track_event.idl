// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#rtctrackevent
[
    Exposed=Window
] interface RTCTrackEvent : Event {
    constructor(DOMString type, RTCTrackEventInit eventInitDict);
    readonly attribute RTCRtpReceiver           receiver;
    readonly attribute MediaStreamTrack         track;
    [SameObject, SaveSameObject]
    readonly attribute FrozenArray<MediaStream> streams;
    readonly attribute RTCRtpTransceiver        transceiver;
};
