// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#dom-rtcrtpcontributingsource
dictionary RTCRtpContributingSource {
    required DOMHighResTimeStamp timestamp;
    required unsigned long       source;
             double              audioLevel;
    required unsigned long       rtpTimestamp;
    // https://w3c.github.io/webrtc-extensions/#dom-rtcrtpcontributingsource-capturetimestamp
    DOMHighResTimeStamp captureTimestamp;
    // https://w3c.github.io/webrtc-extensions/#dom-rtcrtpcontributingsource-sendercapturetimeoffset
    DOMHighResTimeStamp senderCaptureTimeOffset;
};
