// Copyright 2024 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-encoded-transform/#ref-for-dedicatedworkerglobalscope

[ImplementedAs=DedicatedWorkerGlobalScopeRTCTransform, RuntimeEnabled=RTCRtpScriptTransform]
partial interface DedicatedWorkerGlobalScope {
    attribute EventHandler onrtctransform;
};