include_rules = [
    "-third_party/blink/renderer/modules",
    "+third_party/blink/renderer/modules/crypto",
    "+third_party/blink/renderer/modules/event_modules.h",
    "+third_party/blink/renderer/modules/event_target_modules.h",
    "+third_party/blink/renderer/modules/mediastream",
    "+third_party/blink/renderer/modules/modules_export.h",
    "+third_party/blink/renderer/modules/peerconnection",
    "+third_party/blink/renderer/modules/p2p",
    "+third_party/blink/renderer/modules/webrtc",

    "+base/hash/md5.h",
    "+base/lazy_instance.h",
    "+base/power_monitor/power_observer.h",
    # TODO(crbug.com/787254): Replace StringPrintf uses here.
    "+base/strings/stringprintf.h",
    # TODO(crbug.com/787254): Remove the use of base::CurrentThread.
    "+base/task/current_thread.h",
    "+base/threading/thread.h",
    "+base/unguessable_token.h",
    "+base/values.h",
    "+crypto/openssl_util.h",
    "+components/webrtc/thread_wrapper.h",
    "+media/base",
    "+media/media_buildflags.h",
    "+media/mojo/clients/mojo_video_encoder_metrics_provider.h",
    "+media/mojo/mojom/webrtc_video_perf.mojom-blink.h",
    "+media/video/gpu_video_accelerator_factories.h",
    "+net/third_party/quiche/src/quiche/quic",
    "+net/third_party/quiche/src/quiche/common",
    "+net/net_buildflags.h",
    "+net/quic/chromium",
    "+net/quic",
    "+net/test",
    "+services/metrics/public/cpp/ukm_builders.h",
]

specific_include_rules = {
    "rtc_dtls_transport.cc": [
        "+third_party/boringssl/src/include",
    ],
    ".*test.*": [
        # TODO(crbug.com/1294176): Remove when no longer needed.
        "+base/cfi_buildflags.h",
        "+base/run_loop.h",
        "+base/test/scoped_run_loop_timeout.h",
        "+base/test/test_timeouts.h",
        "+base/threading/thread.h",
        "+media/audio/audio_sink_parameters.h",
        "+media/audio/audio_source_parameters.h",
        "+ui/gfx/color_space.h",
    ],
}
