// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// http://w3c.github.io/webrtc-pc/#rtcdtlstransport-interface

enum RTCDtlsTransportState {
    "new",
    "connecting",
    "connected",
    "closed",
    "failed"
};

[
    Exposed=Window
] interface RTCDtlsTransport : EventTarget {
    readonly attribute RTCIceTransport iceTransport;
    readonly attribute RTCDtlsTransportState state;
    sequence<ArrayBuffer> getRemoteCertificates();
    attribute EventHandler onstatechange;
    attribute EventHandler onerror;
};
