// Copyright 2022 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "third_party/blink/renderer/modules/peerconnection/rtc_ice_candidate.h"

#include "testing/gmock/include/gmock/gmock.h"
#include "testing/gtest/include/gtest/gtest.h"
#include "third_party/blink/renderer/bindings/modules/v8/v8_rtc_ice_server_transport_protocol.h"
#include "third_party/blink/renderer/platform/peerconnection/rtc_ice_candidate_platform.h"
#include "third_party/blink/renderer/modules/peerconnection/external_ip_cache.h"
#include "base/command_line.h"

namespace blink {

constexpr char kUdpRelayCandidateStr[] =
    "candidate:a0+B/3 1 udp 41623807 ******* 2345 typ relay raddr "
    "*********** rport 12345";
constexpr char kUrl[] = "bogusurl";
constexpr char kMid[] = "somemid";
constexpr char kUsernameFragment[] = "u";
constexpr int kSdpMLineIndex = 0;

TEST(RTCIceCandidateTest, Url) {
  RTCIceCandidate* candidate(
      RTCIceCandidate::Create(MakeGarbageCollected<RTCIceCandidatePlatform>(
          kUdpRelayCandidateStr, kMid, kSdpMLineIndex, kUsernameFragment,
          kUrl)));
  EXPECT_EQ(candidate->url(), String(kUrl));
}

TEST(RTCIceCandidateTest, RelayProtocol) {
  RTCIceCandidate* candidate(
      RTCIceCandidate::Create(MakeGarbageCollected<RTCIceCandidatePlatform>(
          kUdpRelayCandidateStr, kMid, kSdpMLineIndex, kUsernameFragment,
          kUrl)));
  EXPECT_EQ(candidate->relayProtocol(),
            V8RTCIceServerTransportProtocol::Enum::kUdp);
}

// 测试外部 IP 缓存功能
TEST(ExternalIPCacheTest, Singleton) {
  ExternalIPCache* cache1 = ExternalIPCache::GetInstance();
  ExternalIPCache* cache2 = ExternalIPCache::GetInstance();
  EXPECT_EQ(cache1, cache2);
}

TEST(ExternalIPCacheTest, InitialState) {
  ExternalIPCache* cache = ExternalIPCache::GetInstance();
  EXPECT_FALSE(cache->HasValidCache());
  EXPECT_TRUE(cache->GetCachedIP().empty());
}

// 测试 IP 替换功能（需要模拟命令行参数）
class RTCIceCandidateIPReplacementTest : public ::testing::Test {
 protected:
  void SetUp() override {
    // 保存原始命令行
    original_command_line_ = base::CommandLine::ForCurrentProcess();
  }

  void TearDown() override {
    // 恢复原始命令行
    base::CommandLine::Reset();
    base::CommandLine::Init(0, nullptr);
  }

  void SetCommandLineSwitch(const std::string& switch_name, const std::string& value) {
    base::CommandLine::Reset();
    base::CommandLine::Init(0, nullptr);
    base::CommandLine::ForCurrentProcess()->AppendSwitchASCII(switch_name, value);
  }

 private:
  base::CommandLine* original_command_line_;
};

TEST_F(RTCIceCandidateIPReplacementTest, WebRTCIPReplacement) {
  SetCommandLineSwitch("webrtc-ip", "***********");

  RTCIceCandidate* candidate(
      RTCIceCandidate::Create(MakeGarbageCollected<RTCIceCandidatePlatform>(
          kUdpRelayCandidateStr, kMid, kSdpMLineIndex, kUsernameFragment,
          kUrl)));

  // 检查 IP 是否被替换
  String candidate_str = candidate->candidate();
  EXPECT_TRUE(candidate_str.Contains("***********"));
  EXPECT_FALSE(candidate_str.Contains("*******"));
}

TEST_F(RTCIceCandidateIPReplacementTest, ProxyServerDetection) {
  SetCommandLineSwitch("proxy-server", "http://proxy.example.com:8080");

  ExternalIPCache* cache = ExternalIPCache::GetInstance();
  cache->Initialize();

  // 注意：在实际测试中，这里会触发异步网络请求
  // 在单元测试环境中，我们使用模拟的 IP
}

}  // namespace blink
