// Copyright 2024 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

dictionary RTCRtpPacketInit {
  boolean marker = false;
  required octet payloadType;
  required unsigned long timestamp;
  sequence<unsigned long> csrcs = [];

  sequence<RTCRtpHeaderExtensionInit> headerExtensions = [];
  required AllowSharedBufferSource payload;
};
