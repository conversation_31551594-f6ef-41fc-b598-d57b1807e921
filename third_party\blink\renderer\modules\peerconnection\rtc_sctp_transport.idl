// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// http://w3c.github.io/webrtc-pc/#dom-rtcsctptransportstate
enum RTCSctpTransportState {
    "connecting",
    "connected",
    "closed"
};

// http://w3c.github.io/webrtc-pc/#rtcdtlstransport-interface

[
    Exposed=Window
] interface RTCSctpTransport : EventTarget {
    readonly        attribute RTCDtlsTransport transport;
    readonly        attribute RTCSctpTransportState state;
    readonly        attribute unrestricted double maxMessageSize;
    readonly        attribute unsigned short? maxChannels;
                    attribute EventHandler     onstatechange;
};
