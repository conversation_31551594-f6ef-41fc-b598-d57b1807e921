# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//third_party/blink/renderer/modules/modules.gni")

blink_modules_sources("peerconnection") {
  sources = [
    "adapters/dtls_transport_proxy.cc",
    "adapters/dtls_transport_proxy.h",
    "adapters/ice_transport_adapter.h",
    "adapters/ice_transport_adapter_cross_thread_factory.h",
    "adapters/ice_transport_adapter_impl.cc",
    "adapters/ice_transport_adapter_impl.h",
    "adapters/ice_transport_host.cc",
    "adapters/ice_transport_host.h",
    "adapters/ice_transport_proxy.cc",
    "adapters/ice_transport_proxy.h",
    "adapters/sctp_transport_proxy.cc",
    "adapters/sctp_transport_proxy.h",
    "adapters/web_rtc_cross_thread_copier.cc",
    "adapters/web_rtc_cross_thread_copier.h",
    "byte_buffer_queue.cc",
    "byte_buffer_queue.h",
    "dedicated_worker_global_scope_rtc_rtp_transport_processor.h",
    "dedicated_worker_global_scope_rtc_transform.h",
    "identifiability_metrics.cc",
    "identifiability_metrics.h",
    "intercepting_network_controller.cc",
    "intercepting_network_controller.h",
    "media_stream_remote_video_source.cc",
    "media_stream_remote_video_source.h",
    "media_stream_track_metrics.cc",
    "media_stream_track_metrics.h",
    "media_stream_video_webrtc_sink.cc",
    "media_stream_video_webrtc_sink.h",
    "peer_connection_dependency_factory.cc",
    "peer_connection_dependency_factory.h",
    "peer_connection_features.cc",
    "peer_connection_features.h",
    "peer_connection_tracker.cc",
    "peer_connection_tracker.h",
    "peer_connection_util.cc",
    "peer_connection_util.h",
    "rtc_certificate.cc",
    "rtc_certificate.h",
    "rtc_certificate_generator.cc",
    "rtc_certificate_generator.h",
    "rtc_data_channel.cc",
    "rtc_data_channel.h",
    "rtc_data_channel_attachment.cc",
    "rtc_data_channel_attachment.h",
    "rtc_data_channel_event.cc",
    "rtc_data_channel_event.h",
    "rtc_data_channel_transfer_list.cc",
    "rtc_data_channel_transfer_list.h",
    "rtc_dtls_transport.cc",
    "rtc_dtls_transport.h",
    "rtc_dtmf_sender.cc",
    "rtc_dtmf_sender.h",
    "rtc_dtmf_tone_change_event.cc",
    "rtc_dtmf_tone_change_event.h",
    "rtc_encoded_audio_frame.cc",
    "rtc_encoded_audio_frame.h",
    "rtc_encoded_audio_frame_delegate.cc",
    "rtc_encoded_audio_frame_delegate.h",
    "rtc_encoded_audio_receiver_sink_optimizer.cc",
    "rtc_encoded_audio_receiver_sink_optimizer.h",
    "rtc_encoded_audio_receiver_source_optimizer.cc",
    "rtc_encoded_audio_receiver_source_optimizer.h",
    "rtc_encoded_audio_sender_sink_optimizer.cc",
    "rtc_encoded_audio_sender_sink_optimizer.h",
    "rtc_encoded_audio_sender_source_optimizer.cc",
    "rtc_encoded_audio_sender_source_optimizer.h",
    "rtc_encoded_audio_underlying_sink.cc",
    "rtc_encoded_audio_underlying_sink.h",
    "rtc_encoded_audio_underlying_source.cc",
    "rtc_encoded_audio_underlying_source.h",
    "rtc_encoded_underlying_sink_wrapper.cc",
    "rtc_encoded_underlying_sink_wrapper.h",
    "rtc_encoded_underlying_source_wrapper.cc",
    "rtc_encoded_underlying_source_wrapper.h",
    "rtc_encoded_video_frame.cc",
    "rtc_encoded_video_frame.h",
    "rtc_encoded_video_frame_delegate.cc",
    "rtc_encoded_video_frame_delegate.h",
    "rtc_encoded_video_receiver_sink_optimizer.cc",
    "rtc_encoded_video_receiver_sink_optimizer.h",
    "rtc_encoded_video_receiver_source_optimizer.cc",
    "rtc_encoded_video_receiver_source_optimizer.h",
    "rtc_encoded_video_sender_sink_optimizer.cc",
    "rtc_encoded_video_sender_sink_optimizer.h",
    "rtc_encoded_video_sender_source_optimizer.cc",
    "rtc_encoded_video_sender_source_optimizer.h",
    "rtc_encoded_video_underlying_sink.cc",
    "rtc_encoded_video_underlying_sink.h",
    "rtc_encoded_video_underlying_source.cc",
    "rtc_encoded_video_underlying_source.h",
    "rtc_error.cc",
    "rtc_error.h",
    "rtc_error_event.cc",
    "rtc_error_event.h",
    "rtc_error_util.cc",
    "rtc_error_util.h",
    "external_ip_cache.cc",
    "external_ip_cache.h",
    "rtc_ice_candidate.cc",
    "rtc_ice_candidate.h",
    "rtc_ice_transport.cc",
    "rtc_ice_transport.h",
    "rtc_peer_connection.cc",
    "rtc_peer_connection.h",
    "rtc_peer_connection_controller.cc",
    "rtc_peer_connection_controller.h",
    "rtc_peer_connection_handler.cc",
    "rtc_peer_connection_handler.h",
    "rtc_peer_connection_ice_error_event.cc",
    "rtc_peer_connection_ice_error_event.h",
    "rtc_peer_connection_ice_event.cc",
    "rtc_peer_connection_ice_event.h",
    "rtc_rtp_acks.h",
    "rtc_rtp_receiver.cc",
    "rtc_rtp_receiver.h",
    "rtc_rtp_receiver_impl.cc",
    "rtc_rtp_receiver_impl.h",
    "rtc_rtp_script_transform.cc",
    "rtc_rtp_script_transform.h",
    "rtc_rtp_script_transformer.cc",
    "rtc_rtp_script_transformer.h",
    "rtc_rtp_send_result.cc",
    "rtc_rtp_send_result.h",
    "rtc_rtp_send_stream.cc",
    "rtc_rtp_send_stream.h",
    "rtc_rtp_sender.cc",
    "rtc_rtp_sender.h",
    "rtc_rtp_sender_impl.cc",
    "rtc_rtp_sender_impl.h",
    "rtc_rtp_sent.cc",
    "rtc_rtp_sent.h",
    "rtc_rtp_transceiver.cc",
    "rtc_rtp_transceiver.h",
    "rtc_rtp_transceiver_impl.cc",
    "rtc_rtp_transceiver_impl.h",
    "rtc_rtp_transport.cc",
    "rtc_rtp_transport.h",
    "rtc_rtp_transport_processor.cc",
    "rtc_rtp_transport_processor.h",
    "rtc_sctp_transport.cc",
    "rtc_sctp_transport.h",
    "rtc_session_description.cc",
    "rtc_session_description.h",
    "rtc_session_description_enums.h",
    "rtc_session_description_request_impl.cc",
    "rtc_session_description_request_impl.h",
    "rtc_session_description_request_promise_impl.cc",
    "rtc_session_description_request_promise_impl.h",
    "rtc_stats_report.cc",
    "rtc_stats_report.h",
    "rtc_track_event.cc",
    "rtc_track_event.h",
    "rtc_transform_event.cc",
    "rtc_transform_event.h",
    "rtc_void_request_impl.cc",
    "rtc_void_request_impl.h",
    "rtc_void_request_promise_impl.cc",
    "rtc_void_request_promise_impl.h",
    "rtp_contributing_source_cache.cc",
    "rtp_contributing_source_cache.h",
    "thermal_resource.cc",
    "thermal_resource.h",
    "transceiver_state_surfacer.cc",
    "transceiver_state_surfacer.h",
    "web_rtc_stats_report_callback_resolver.cc",
    "web_rtc_stats_report_callback_resolver.h",
    "webrtc_media_stream_track_adapter.cc",
    "webrtc_media_stream_track_adapter.h",
    "webrtc_media_stream_track_adapter_map.cc",
    "webrtc_media_stream_track_adapter_map.h",
    "webrtc_set_description_observer.cc",
    "webrtc_set_description_observer.h",
    "webrtc_video_perf_reporter.cc",
    "webrtc_video_perf_reporter.h",
  ]

  public_deps = [ "//third_party/webrtc_overrides:webrtc_component" ]
  deps = [
    "//build:chromecast_buildflags",
    "//components/webrtc:thread_wrapper",
    "//media/mojo/clients:clients",
    "//services/metrics/public/cpp:ukm_builders",
    "//third_party/abseil-cpp:absl",
    "//third_party/blink/public/mojom:web_feature_mojo_bindings",
    "//third_party/blink/renderer/modules/crypto",
    "//third_party/blink/renderer/modules/mediastream",
    "//third_party/blink/renderer/modules/webrtc",
  ]

  allow_circular_includes_from =
      [ "//third_party/blink/renderer/modules/mediastream" ]
}

source_set("test_support") {
  testonly = true

  sources = [
    "fake_rtc_rtp_transceiver_impl.cc",
    "fake_rtc_rtp_transceiver_impl.h",
    "mock_data_channel_impl.cc",
    "mock_data_channel_impl.h",
    "mock_peer_connection_dependency_factory.cc",
    "mock_peer_connection_dependency_factory.h",
    "mock_peer_connection_impl.cc",
    "mock_peer_connection_impl.h",
    "mock_rtc_peer_connection_handler_client.cc",
    "mock_rtc_peer_connection_handler_client.h",
    "mock_rtc_peer_connection_handler_platform.cc",
    "mock_rtc_peer_connection_handler_platform.h",
    "test_webrtc_stats_report_obtainer.cc",
    "test_webrtc_stats_report_obtainer.h",
  ]

  deps = [
    "//base",
    "//base/test:test_support",
    "//testing/gmock",
    "//third_party/blink/public:test_headers",
    "//third_party/blink/renderer/modules",
    "//third_party/blink/renderer/modules/mediastream:test_support",
    "//third_party/blink/renderer/platform",
    "//third_party/blink/renderer/platform:test_support",
    "//third_party/webrtc_overrides:webrtc_component",
  ]

  configs += [ "//third_party/blink/renderer:inside_blink" ]
}
