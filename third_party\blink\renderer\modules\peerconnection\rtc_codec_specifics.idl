// Copyright 2022 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// TODO(https://crbug.com/webrtc/14709): Support more codecs.
typedef RTCCodecSpecificsVP8 RTCCodecSpecifics;

dictionary RTCCodecSpecificsVP8 {
    boolean nonReference;
    short pictureId;
    short tl0PicIdx;
    octet temporalIdx;
    boolean layerSync;
    long keyIdx;
    long partitionId;
    boolean beginningOfPartition;
};
