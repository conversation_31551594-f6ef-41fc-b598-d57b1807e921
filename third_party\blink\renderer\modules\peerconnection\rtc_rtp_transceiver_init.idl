// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#dom-rtcrtptransceiverinit
dictionary RTCRtpTransceiverInit {
    // RTCRtpTransceiverDirection is defined in rtc_rtp_transceiver.idl.
    RTCRtpTransceiverDirection direction = "sendrecv";
    sequence<MediaStream> streams = [];
    sequence<RTCRtpEncodingParameters> sendEncodings = [];
};
