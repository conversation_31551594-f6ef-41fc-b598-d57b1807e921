// Copyright 2024 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-encoded-transform/#rtcrtpscripttransform

[Exposed=Window, RuntimeEnabled=RTCRtpScriptTransform]
interface RTCRtpScriptTransform {
    [CallWith=ScriptState, RaisesException] constructor(Worker worker, optional any options, optional sequence<object> transfer);
};