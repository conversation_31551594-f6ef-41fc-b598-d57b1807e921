/*
 * Copyright (C) 2024 The Chromium Authors. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer
 *    in the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name of Google Inc. nor the names of its contributors
 *    may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * <PERSON>ECIA<PERSON>, E<PERSON>EMPLAR<PERSON>, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "third_party/blink/renderer/modules/peerconnection/external_ip_cache.h"

#include <iostream>
#include <regex>
#include <algorithm>
#include <cctype>
#include "base/command_line.h"
#include "base/functional/bind.h"
#include "base/time/time.h"
#include "net/traffic_annotation/network_traffic_annotation.h"
#include "url/gurl.h"

namespace blink {

namespace {
// 网络流量注解
constexpr net::NetworkTrafficAnnotationTag kExternalIPTrafficAnnotation =
    net::DefineNetworkTrafficAnnotation("external_ip_fetch", R"(
      semantics {
        sender: "WebRTC External IP Cache"
        description: "Fetches external IP address for WebRTC connections."
        trigger: "When proxy-server parameter is specified."
        data: "HTTP GET request to IP detection service."
        destination: WEBSITE
      }
      policy {
        cookies_allowed: NO
        setting: "This feature can be disabled by not using proxy-server parameter."
      })");
}

// static
ExternalIPCache* ExternalIPCache::GetInstance() {
  static ExternalIPCache instance;
  return &instance;
}

void ExternalIPCache::Initialize() {
  base::CommandLine* command_line = base::CommandLine::ForCurrentProcess();
  if (command_line->HasSwitch("proxy-server")) {
    proxy_server_ = command_line->GetSwitchValueASCII("proxy-server");
    std::cerr << "检测到 proxy-server 参数: " << proxy_server_ << std::endl;
    
    // 获取当前线程的任务运行器
    task_runner_ = base::SingleThreadTaskRunner::GetCurrentDefault();
    
    // 立即获取一次 IP
    FetchExternalIP();
    
    // 启动定时器，每分钟更新一次
    StartPeriodicUpdate();
  }
}

std::string ExternalIPCache::GetCachedIP() {
  base::AutoLock lock(lock_);
  return cached_ip_;
}

bool ExternalIPCache::HasValidCache() {
  base::AutoLock lock(lock_);
  return !cached_ip_.empty();
}

void ExternalIPCache::SetURLLoaderFactory(network::mojom::URLLoaderFactory* factory) {
  url_loader_factory_ = factory;
}

void ExternalIPCache::StartPeriodicUpdate() {
  if (task_runner_ && !is_updating_) {
    is_updating_ = true;
    task_runner_->PostDelayedTask(
        FROM_HERE,
        base::BindOnce(&ExternalIPCache::PeriodicUpdate, 
                       base::Unretained(this)),
        base::Minutes(1));
  }
}

void ExternalIPCache::PeriodicUpdate() {
  FetchExternalIP();
  // 安排下一次更新
  if (task_runner_) {
    task_runner_->PostDelayedTask(
        FROM_HERE,
        base::BindOnce(&ExternalIPCache::PeriodicUpdate, 
                       base::Unretained(this)),
        base::Minutes(1));
  }
}

void ExternalIPCache::FetchExternalIP() {
  std::cerr << "开始异步获取外部 IP..." << std::endl;
  
  if (url_loader_factory_) {
    // 使用真实的网络请求
    auto resource_request = std::make_unique<network::ResourceRequest>();
    resource_request->url = GURL("https://api.ipify.org?format=text");
    resource_request->method = "GET";
    resource_request->credentials_mode = network::mojom::CredentialsMode::kOmit;
    
    // 创建 URLLoader
    auto url_loader = network::SimpleURLLoader::Create(
        std::move(resource_request), 
        kExternalIPTrafficAnnotation);
    
    // 设置超时
    url_loader->SetTimeoutDuration(base::Seconds(10));
    
    // 异步下载
    auto* url_loader_ptr = url_loader.get();
    url_loader_ptr->DownloadToString(
        url_loader_factory_,
        base::BindOnce(&ExternalIPCache::OnIPFetched, 
                       base::Unretained(this),
                       std::move(url_loader)),
        1024);  // 最大 1KB
  } else {
    // 如果没有 URLLoaderFactory，使用模拟请求
    SimulateFetchExternalIP();
  }
}

void ExternalIPCache::OnIPFetched(std::unique_ptr<network::SimpleURLLoader> url_loader,
                                  std::unique_ptr<std::string> response_body) {
  if (response_body && !response_body->empty()) {
    std::string ip = *response_body;
    // 去除换行符和空格
    ip.erase(std::remove_if(ip.begin(), ip.end(), ::isspace), ip.end());
    
    if (IsValidIPAddress(ip)) {
      base::AutoLock lock(lock_);
      cached_ip_ = ip;
      last_update_time_ = base::Time::Now();
      std::cerr << "成功获取并缓存外部 IP: " << cached_ip_ << std::endl;
    } else {
      std::cerr << "获取的 IP 格式无效: " << ip << std::endl;
    }
  } else {
    std::cerr << "获取外部 IP 失败" << std::endl;
  }
}

void ExternalIPCache::SimulateFetchExternalIP() {
  // 模拟异步网络请求
  if (task_runner_) {
    task_runner_->PostDelayedTask(
        FROM_HERE,
        base::BindOnce(&ExternalIPCache::OnSimulatedIPFetched, 
                       base::Unretained(this)),
        base::Seconds(1));  // 模拟 1 秒的网络延迟
  }
}

void ExternalIPCache::OnSimulatedIPFetched() {
  // 这里应该是真实的网络请求结果
  // 为了演示，我们使用一个示例 IP
  std::string simulated_ip = "***********";  // RFC 5737 测试用 IP
  
  std::cerr << "模拟获取到外部 IP: " << simulated_ip << std::endl;
  
  if (IsValidIPAddress(simulated_ip)) {
    base::AutoLock lock(lock_);
    cached_ip_ = simulated_ip;
    last_update_time_ = base::Time::Now();
    std::cerr << "成功缓存外部 IP: " << cached_ip_ << std::endl;
  }
}

bool ExternalIPCache::IsValidIPAddress(const std::string& ip) {
  std::regex ip_pattern(
      R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.)"
      R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.)"
      R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.)"
      R"((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9]))");
  
  return std::regex_match(ip, ip_pattern);
}

}  // namespace blink
