/*
 * Copyright (C) 2024 The Chromium Authors. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer
 *    in the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name of Google Inc. nor the names of its contributors
 *    may be used to endorse or promote products derived from this
 *    software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * <PERSON>ECIA<PERSON>, E<PERSON>EMPLAR<PERSON>, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef THIRD_PARTY_BLINK_RENDERER_MODULES_PEERCONNECTION_EXTERNAL_IP_CACHE_H_
#define THIRD_PARTY_BLINK_RENDERER_MODULES_PEERCONNECTION_EXTERNAL_IP_CACHE_H_

#include <string>
#include <memory>
#include "base/synchronization/lock.h"
#include "base/time/time.h"
#include "base/task/single_thread_task_runner.h"
#include "services/network/public/cpp/simple_url_loader.h"
#include "services/network/public/cpp/resource_request.h"
#include "services/network/public/mojom/url_loader_factory.mojom.h"

namespace blink {

// 外部 IP 缓存管理器
// 负责异步获取外部 IP 地址并缓存，支持定期更新
class ExternalIPCache {
 public:
  static ExternalIPCache* GetInstance();

  // 初始化缓存系统
  void Initialize();

  // 获取缓存的 IP 地址
  std::string GetCachedIP();

  // 检查是否有有效的缓存
  bool HasValidCache();

  // 设置 URLLoaderFactory（用于网络请求）
  void SetURLLoaderFactory(network::mojom::URLLoaderFactory* factory);

 private:
  ExternalIPCache() = default;
  ~ExternalIPCache() = default;

  // 禁止拷贝和赋值
  ExternalIPCache(const ExternalIPCache&) = delete;
  ExternalIPCache& operator=(const ExternalIPCache&) = delete;

  // 启动定期更新任务
  void StartPeriodicUpdate();

  // 定期更新回调
  void PeriodicUpdate();

  // 异步获取外部 IP
  void FetchExternalIP();

  // 网络请求完成回调
  void OnIPFetched(std::unique_ptr<network::SimpleURLLoader> url_loader,
                   std::unique_ptr<std::string> response_body);

  // 模拟网络请求（用于测试）
  void SimulateFetchExternalIP();
  void OnSimulatedIPFetched();

  // 验证 IP 地址格式
  bool IsValidIPAddress(const std::string& ip);

  std::string proxy_server_;
  std::string cached_ip_;
  base::Time last_update_time_;
  base::Lock lock_;
  network::mojom::URLLoaderFactory* url_loader_factory_ = nullptr;
  
  // 用于管理定期更新任务
  scoped_refptr<base::SingleThreadTaskRunner> task_runner_;
  bool is_updating_ = false;
};

}  // namespace blink

#endif  // THIRD_PARTY_BLINK_RENDERER_MODULES_PEERCONNECTION_EXTERNAL_IP_CACHE_H_
