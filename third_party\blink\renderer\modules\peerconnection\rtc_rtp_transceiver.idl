// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#dom-rtcrtptransceiverdirection
enum RTCRtpTransceiverDirection {
    "sendrecv",
    "sendonly",
    "recvonly",
    "inactive",
    "stopped"
};

// https://w3c.github.io/webrtc-pc/#dom-rtcrtptransceiver
[Exposed=Window]
interface RTCRtpTransceiver {
    readonly attribute DOMString? mid;
    [SameObject] readonly attribute RTCRtpSender sender;
    [SameObject] readonly attribute RTCRtpReceiver receiver;
    // Non-standard
    [Measure] readonly attribute boolean stopped;
    [Measure, RaisesException=Setter] attribute RTCRtpTransceiverDirection direction;
    readonly attribute RTCRtpTransceiverDirection? currentDirection;
    [Measure, RaisesException] void stop();
    [RaisesException] void setCodecPreferences(sequence<RTCRtpCodecCapability> codecs);
    sequence<RTCRtpHeaderExtensionCapability> getHeaderExtensionsToNegotiate();
    [RaisesException] void setHeaderExtensionsToNegotiate(
            sequence<RTCRtpHeaderExtensionCapability> extensions);
    sequence<RTCRtpHeaderExtensionCapability> getNegotiatedHeaderExtensions();
};
