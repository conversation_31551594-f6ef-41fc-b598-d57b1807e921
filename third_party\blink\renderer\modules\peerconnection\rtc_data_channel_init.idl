// Copyright 2016 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// https://w3c.github.io/webrtc-pc/#idl-def-rtcdatachannelinit

dictionary RTCDataChannelInit {
    boolean ordered = true;
    unsigned short maxPacketLifeTime;
    unsigned short maxRetransmits;
    USVString protocol = "";
    boolean negotiated = false;
    [EnforceRange] unsigned short id;

    // https://w3c.github.io/webrtc-priority/#datachannel
    [RuntimeEnabled=RTCDataChannelPriority] RTCPriorityType priority = "low";
};
